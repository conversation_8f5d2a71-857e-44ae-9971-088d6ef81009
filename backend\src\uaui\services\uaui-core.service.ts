import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

// Interfaces
import { 
  UauiRequest, 
  UauiResponse, 
  SessionContext, 
  ProcessingPipelineStep,
  ProcessingStepResult,
  StreamingResponse,
  UauiError
} from '../interfaces/uaui.interface';

// Services
import { AIProviderManager } from '../../ai-providers/services/ai-provider-manager';
import { ContextManagerService } from './context-manager.service';
import { PromptTemplateService } from './prompt-template.service';
import { ConversationHistoryService } from './conversation-history.service';
import { EventBusService } from '../../websocket/services/event-bus.service';

// Entities
import { Session } from '../../entities/session.entity';
import { Message } from '../../entities/message.entity';
import { Agent } from '../../entities/agent.entity';

// AI Provider interfaces
import { AIMessage } from '../../ai-providers/interfaces/provider.interface';

@Injectable()
export class UauiCoreService {
  private readonly logger = new Logger(UauiCoreService.name);
  private readonly processingPipeline: ProcessingPipelineStep[] = [];

  constructor(
    @InjectRepository(Session)
    private readonly sessionRepository: Repository<Session>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    private readonly aiProviderManager: AIProviderManager,
    private readonly contextManager: ContextManagerService,
    private readonly promptTemplateService: PromptTemplateService,
    private readonly conversationHistoryService: ConversationHistoryService,
    private readonly eventBus: EventBusService,
  ) {
    this.initializePipeline();
  }

  /**
   * Process a UAUI request through the complete pipeline
   */
  async processRequest(request: UauiRequest): Promise<UauiResponse> {
    const startTime = Date.now();
    
    try {
      this.logger.debug(`Processing UAUI request ${request.requestId} for session ${request.sessionId}`);

      // Get or create session context
      const context = await this.contextManager.getSessionContext(request.sessionId);
      if (!context) {
        throw new Error(`Session ${request.sessionId} not found`);
      }

      // Execute processing pipeline
      const pipelineResult = await this.executePipeline(request, context);
      if (!pipelineResult.success) {
        throw new Error(pipelineResult.error || 'Pipeline execution failed');
      }

      // Generate AI response
      const aiResponse = await this.generateAIResponse(request, context);

      // Save message to conversation history
      await this.saveConversationMessages(request, aiResponse, context);

      // Update session context
      await this.contextManager.updateSessionContext(request.sessionId, {
        lastActivity: new Date(),
        metadata: {
          ...context.metadata,
          lastRequestId: request.requestId,
          lastProcessingTime: Date.now() - startTime,
        }
      });

      const response: UauiResponse = {
        requestId: request.requestId,
        sessionId: request.sessionId,
        response: aiResponse.content,
        streaming: false,
        context: context.variables,
        metadata: {
          agentId: request.agentId,
          processingSteps: pipelineResult.data?.steps || [],
        },
        timestamp: new Date(),
        processingTime: Date.now() - startTime,
        tokenUsage: aiResponse.usage,
      };

      this.logger.debug(`UAUI request ${request.requestId} processed successfully in ${response.processingTime}ms`);
      return response;

    } catch (error) {
      this.logger.error(`Error processing UAUI request ${request.requestId}:`, error);
      
      const uauiError: UauiError = {
        code: 'PROCESSING_ERROR',
        message: error.message,
        details: { stack: error.stack },
        timestamp: new Date(),
        requestId: request.requestId,
        sessionId: request.sessionId,
      };

      // Emit error event
      await this.eventBus.emit({
        type: 'error' as any,
        sessionId: request.sessionId,
        userId: request.userId,
        tenantId: request.tenantId,
        timestamp: new Date(),
        requestId: request.requestId,
        error: uauiError.message,
        errorType: 'processing',
        recoverable: true,
      });

      throw uauiError;
    }
  }

  /**
   * Process a streaming UAUI request
   */
  async processStreamingRequest(request: UauiRequest): Promise<StreamingResponse> {
    try {
      this.logger.debug(`Processing streaming UAUI request ${request.requestId}`);

      // Get session context
      const context = await this.contextManager.getSessionContext(request.sessionId);
      if (!context) {
        throw new Error(`Session ${request.sessionId} not found`);
      }

      // Execute pipeline
      await this.executePipeline(request, context);

      // Generate streaming response
      const streamingResponse = await this.generateStreamingAIResponse(request, context);

      return {
        requestId: request.requestId,
        sessionId: request.sessionId,
        chunks: streamingResponse,
        metadata: {
          agentId: request.agentId,
        },
      };

    } catch (error) {
      this.logger.error(`Error processing streaming UAUI request ${request.requestId}:`, error);
      throw error;
    }
  }

  /**
   * Initialize the processing pipeline
   */
  private initializePipeline(): void {
    this.processingPipeline.push(
      {
        name: 'context_injection',
        execute: async (request: UauiRequest, context: SessionContext) => {
          await this.contextManager.injectContext(request, context);
          return { success: true, shouldContinue: true };
        }
      },
      {
        name: 'memory_management',
        execute: async (request: UauiRequest, context: SessionContext) => {
          await this.contextManager.manageMemory(context);
          return { success: true, shouldContinue: true };
        }
      },
      {
        name: 'conversation_history',
        execute: async (request: UauiRequest, context: SessionContext) => {
          await this.conversationHistoryService.loadHistory(context);
          return { success: true, shouldContinue: true };
        }
      },
      {
        name: 'context_pruning',
        execute: async (request: UauiRequest, context: SessionContext) => {
          await this.contextManager.pruneContext(context);
          return { success: true, shouldContinue: true };
        }
      }
    );
  }

  /**
   * Execute the processing pipeline
   */
  private async executePipeline(
    request: UauiRequest, 
    context: SessionContext
  ): Promise<ProcessingStepResult> {
    const steps: string[] = [];

    for (const step of this.processingPipeline) {
      try {
        this.logger.debug(`Executing pipeline step: ${step.name}`);
        const result = await step.execute(request, context);
        steps.push(step.name);

        if (!result.success || !result.shouldContinue) {
          return {
            success: result.success,
            error: result.error,
            data: { steps },
            shouldContinue: false,
          };
        }
      } catch (error) {
        this.logger.error(`Pipeline step ${step.name} failed:`, error);
        return {
          success: false,
          error: `Pipeline step ${step.name} failed: ${error.message}`,
          data: { steps },
          shouldContinue: false,
        };
      }
    }

    return {
      success: true,
      data: { steps },
      shouldContinue: true,
    };
  }

  /**
   * Generate AI response using the AI provider
   */
  private async generateAIResponse(request: UauiRequest, context: SessionContext): Promise<any> {
    // Compile prompt template
    const compiledPrompt = await this.promptTemplateService.compilePrompt(
      request,
      context
    );

    // Generate response using AI provider
    const response = await this.aiProviderManager.generateCompletion(
      compiledPrompt.messages,
      {
        preferredProvider: 'openai', // This should come from agent config
        requireFeatures: { streaming: false },
      },
      request.tenantId
    );

    return response;
  }

  /**
   * Generate streaming AI response
   */
  private async generateStreamingAIResponse(
    request: UauiRequest, 
    context: SessionContext
  ): Promise<AsyncIterable<string>> {
    // Compile prompt template
    const compiledPrompt = await this.promptTemplateService.compilePrompt(
      request,
      context
    );

    // Generate streaming response
    const stream = this.aiProviderManager.generateStreamingCompletion(
      compiledPrompt.messages,
      {
        preferredProvider: 'openai',
        requireFeatures: { streaming: true },
      },
      request.tenantId
    );

    // Transform stream to string chunks
    return this.transformStreamToChunks(stream, request);
  }

  /**
   * Transform AI provider stream to string chunks
   */
  private async* transformStreamToChunks(
    stream: AsyncIterable<any>,
    request: UauiRequest
  ): AsyncIterable<string> {
    let fullResponse = '';
    
    for await (const chunk of stream) {
      if (chunk.content) {
        fullResponse += chunk.content;
        yield chunk.content;
      }
    }

    // Save the complete response after streaming
    // This will be handled by the streaming response handler
  }

  /**
   * Save conversation messages to database
   */
  private async saveConversationMessages(
    request: UauiRequest,
    aiResponse: any,
    context: SessionContext
  ): Promise<void> {
    try {
      // Save user message
      const userMessage = this.messageRepository.create({
        id: uuidv4(),
        sessionId: request.sessionId,
        role: 'user',
        content: request.message,
        timestamp: request.timestamp,
        metadata: request.metadata || {},
      });

      // Save assistant message
      const assistantMessage = this.messageRepository.create({
        id: uuidv4(),
        sessionId: request.sessionId,
        role: 'assistant',
        content: aiResponse.content,
        timestamp: new Date(),
        metadata: {
          requestId: request.requestId,
          tokenUsage: aiResponse.usage,
        },
      });

      await this.messageRepository.save([userMessage, assistantMessage]);

      // Update conversation history in context
      await this.conversationHistoryService.addMessages(context, [
        {
          id: userMessage.id,
          role: 'user',
          content: request.message,
          timestamp: request.timestamp,
          metadata: request.metadata,
        },
        {
          id: assistantMessage.id,
          role: 'assistant',
          content: aiResponse.content,
          timestamp: new Date(),
          metadata: {
            requestId: request.requestId,
            tokenUsage: aiResponse.usage,
          },
        }
      ]);

    } catch (error) {
      this.logger.error('Error saving conversation messages:', error);
      // Don't throw here as the response was successful
    }
  }
}
