import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class TokenUsageDto {
  @ApiProperty({
    description: 'Number of tokens in the prompt',
    example: 150
  })
  promptTokens: number;

  @ApiProperty({
    description: 'Number of tokens in the completion',
    example: 75
  })
  completionTokens: number;

  @ApiProperty({
    description: 'Total number of tokens used',
    example: 225
  })
  totalTokens: number;
}

export class UauiResponseDto {
  @ApiProperty({
    description: 'Unique request identifier',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  requestId: string;

  @ApiProperty({
    description: 'Session identifier',
    example: '123e4567-e89b-12d3-a456-426614174001'
  })
  sessionId: string;

  @ApiProperty({
    description: 'AI-generated response',
    example: 'Hello! I\'d be happy to help you with your project. What specific aspect would you like assistance with?'
  })
  response: string;

  @ApiProperty({
    description: 'Whether the response was streamed',
    example: false
  })
  streaming: boolean;

  @ApiPropertyOptional({
    description: 'Response chunks (for streaming responses)',
    example: ['Hello!', ' I\'d be happy', ' to help you...']
  })
  chunks?: string[];

  @ApiPropertyOptional({
    description: 'Updated context variables',
    example: { lastResponse: 'greeting', responseCount: 1 }
  })
  context?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Response metadata',
    example: { 
      agentId: '123e4567-e89b-12d3-a456-426614174002',
      processingSteps: ['context_injection', 'memory_management', 'conversation_history'],
      model: 'gpt-4'
    }
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Response timestamp',
    example: '2024-01-01T12:00:00Z'
  })
  timestamp: Date;

  @ApiProperty({
    description: 'Processing time in milliseconds',
    example: 1250
  })
  processingTime: number;

  @ApiPropertyOptional({
    description: 'Token usage statistics',
    type: TokenUsageDto
  })
  tokenUsage?: TokenUsageDto;
}

export class StreamingChunkDto {
  @ApiProperty({
    description: 'Chunk type',
    example: 'chunk',
    enum: ['chunk', 'complete', 'error']
  })
  type: 'chunk' | 'complete' | 'error';

  @ApiProperty({
    description: 'Request identifier',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  requestId: string;

  @ApiProperty({
    description: 'Session identifier',
    example: '123e4567-e89b-12d3-a456-426614174001'
  })
  sessionId: string;

  @ApiPropertyOptional({
    description: 'Content chunk (for chunk type)',
    example: 'Hello! I\'d be happy to help'
  })
  content?: string;

  @ApiPropertyOptional({
    description: 'Error message (for error type)',
    example: 'Processing failed due to timeout'
  })
  error?: string;

  @ApiProperty({
    description: 'Chunk timestamp',
    example: '2024-01-01T12:00:00Z'
  })
  timestamp: Date;

  @ApiPropertyOptional({
    description: 'Chunk metadata',
    example: { chunkIndex: 0, totalChunks: 5 }
  })
  metadata?: Record<string, any>;
}

export class SessionContextDto {
  @ApiProperty({
    description: 'Session identifier',
    example: '123e4567-e89b-12d3-a456-426614174001'
  })
  sessionId: string;

  @ApiProperty({
    description: 'Context variables',
    example: { 
      user: { name: 'John Doe', role: 'user' },
      session: { messageCount: 5 },
      preferences: { theme: 'dark' }
    }
  })
  variables: Record<string, any>;

  @ApiProperty({
    description: 'Session memory',
    example: {
      shortTerm: { lastAction: 'search', currentTopic: 'AI' },
      longTerm: { preferences: { language: 'en' } }
    }
  })
  memory: {
    shortTerm: Record<string, any>;
    longTerm: Record<string, any>;
  };

  @ApiProperty({
    description: 'Last activity timestamp',
    example: '2024-01-01T12:00:00Z'
  })
  lastActivity: Date;

  @ApiProperty({
    description: 'Session metadata',
    example: { startTime: '2024-01-01T11:00:00Z', source: 'web' }
  })
  metadata: Record<string, any>;

  @ApiProperty({
    description: 'Conversation history',
    example: [
      { role: 'user', content: 'Hello', timestamp: '2024-01-01T11:00:00Z' },
      { role: 'assistant', content: 'Hi there!', timestamp: '2024-01-01T11:00:01Z' }
    ]
  })
  conversationHistory: Array<{
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    metadata?: Record<string, any>;
  }>;
}

export class ConversationHistoryDto {
  @ApiProperty({
    description: 'Session identifier',
    example: '123e4567-e89b-12d3-a456-426614174001'
  })
  sessionId: string;

  @ApiProperty({
    description: 'Conversation messages',
    example: [
      {
        id: '123e4567-e89b-12d3-a456-426614174003',
        role: 'user',
        content: 'Hello, how are you?',
        timestamp: '2024-01-01T11:00:00Z',
        tokenCount: 5
      },
      {
        id: '123e4567-e89b-12d3-a456-426614174004',
        role: 'assistant',
        content: 'I\'m doing well, thank you for asking!',
        timestamp: '2024-01-01T11:00:01Z',
        tokenCount: 10
      }
    ]
  })
  messages: Array<{
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    metadata?: Record<string, any>;
    tokenCount?: number;
  }>;

  @ApiProperty({
    description: 'Total number of messages',
    example: 2
  })
  totalMessages: number;
}

export class ConversationSearchResultDto {
  @ApiProperty({
    description: 'Session identifier',
    example: '123e4567-e89b-12d3-a456-426614174001'
  })
  sessionId: string;

  @ApiProperty({
    description: 'Search query',
    example: 'project help'
  })
  query: string;

  @ApiProperty({
    description: 'Search results',
    example: [
      {
        id: '123e4567-e89b-12d3-a456-426614174003',
        role: 'user',
        content: 'Can you help me with my project?',
        timestamp: '2024-01-01T11:00:00Z'
      }
    ]
  })
  results: Array<{
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    metadata?: Record<string, any>;
  }>;

  @ApiProperty({
    description: 'Total number of results',
    example: 1
  })
  totalResults: number;
}

export class ConversationStatisticsDto {
  @ApiProperty({
    description: 'Session identifier',
    example: '123e4567-e89b-12d3-a456-426614174001'
  })
  sessionId: string;

  @ApiProperty({
    description: 'Conversation statistics',
    example: {
      totalMessages: 10,
      userMessages: 5,
      assistantMessages: 5,
      systemMessages: 0,
      totalTokens: 500,
      averageMessageLength: 50,
      conversationDuration: 30,
      firstMessage: '2024-01-01T11:00:00Z',
      lastMessage: '2024-01-01T11:30:00Z'
    }
  })
  statistics: {
    totalMessages: number;
    userMessages: number;
    assistantMessages: number;
    systemMessages: number;
    totalTokens: number;
    averageMessageLength: number;
    conversationDuration: number;
    firstMessage?: Date;
    lastMessage?: Date;
  };
}

export class TemplateVariablesDto {
  @ApiProperty({
    description: 'Available template variables',
    example: [
      'request.id',
      'request.message',
      'user.name',
      'user.email',
      'session.id',
      'memory.shortTerm',
      'time.now'
    ]
  })
  variables: string[];

  @ApiProperty({
    description: 'Total number of available variables',
    example: 25
  })
  totalCount: number;
}

export class TemplatePreviewDto {
  @ApiProperty({
    description: 'Compiled template',
    example: 'Hello John Doe, your session session_123 has 5 messages.'
  })
  compiled: string;

  @ApiProperty({
    description: 'Variables used in compilation',
    example: {
      user: { name: 'John Doe' },
      session: { id: 'session_123' },
      conversation: { messageCount: 5 }
    }
  })
  variables: Record<string, any>;

  @ApiProperty({
    description: 'Template validation result',
    example: {
      isValid: true,
      errors: [],
      usedVariables: ['user.name', 'session.id', 'conversation.messageCount']
    }
  })
  validation: {
    isValid: boolean;
    errors: string[];
    usedVariables: string[];
  };
}
