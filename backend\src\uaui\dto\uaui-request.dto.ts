import { 
  IsString, 
  <PERSON><PERSON>ptional, 
  <PERSON>O<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  IsNotEmpty,
  MaxLength,
  ValidateNested
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ProcessRequestDto {
  @ApiProperty({
    description: 'Session ID for the conversation',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsUUID()
  @IsNotEmpty()
  sessionId: string;

  @ApiProperty({
    description: 'User message to process',
    example: 'Hello, can you help me with my project?',
    maxLength: 10000
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(10000)
  message: string;

  @ApiPropertyOptional({
    description: 'Agent ID to use for processing',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  agentId?: string;

  @ApiPropertyOptional({
    description: 'Additional context variables',
    example: { userPreference: 'detailed', language: 'en' }
  })
  @IsOptional()
  @IsObject()
  context?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Request metadata',
    example: { source: 'web', version: '1.0' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class ProcessStreamingRequestDto extends ProcessRequestDto {
  @ApiPropertyOptional({
    description: 'Streaming configuration options',
    example: { chunkSize: 100, bufferTime: 50 }
  })
  @IsOptional()
  @IsObject()
  streamingOptions?: {
    chunkSize?: number;
    bufferTime?: number;
    enableTyping?: boolean;
  };
}

export class UpdateContextDto {
  @ApiProperty({
    description: 'Session ID to update',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsUUID()
  @IsNotEmpty()
  sessionId: string;

  @ApiPropertyOptional({
    description: 'Context variables to update',
    example: { userPreference: 'concise', theme: 'dark' }
  })
  @IsOptional()
  @IsObject()
  variables?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Memory updates',
    example: { 
      shortTerm: { lastAction: 'search' },
      longTerm: { preferences: { format: 'json' } }
    }
  })
  @IsOptional()
  @IsObject()
  memory?: {
    shortTerm?: Record<string, any>;
    longTerm?: Record<string, any>;
  };

  @ApiPropertyOptional({
    description: 'Metadata updates',
    example: { lastUpdated: '2024-01-01T00:00:00Z' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class StoreMemoryDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsUUID()
  @IsNotEmpty()
  sessionId: string;

  @ApiProperty({
    description: 'Memory key',
    example: 'user_preference'
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  key: string;

  @ApiProperty({
    description: 'Memory value',
    example: { theme: 'dark', language: 'en' }
  })
  @IsNotEmpty()
  value: any;

  @ApiProperty({
    description: 'Memory type',
    example: 'shortTerm',
    enum: ['shortTerm', 'longTerm']
  })
  @IsString()
  @IsNotEmpty()
  type: 'shortTerm' | 'longTerm';

  @ApiPropertyOptional({
    description: 'Expiration date for the memory entry',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsString()
  expiresAt?: string;

  @ApiPropertyOptional({
    description: 'Memory metadata',
    example: { source: 'user_input', priority: 'high' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class TemplatePreviewDto {
  @ApiProperty({
    description: 'Template string with variables',
    example: 'Hello {{user.name}}, your session {{session.id}} has {{conversation.messageCount}} messages.'
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(5000)
  template: string;

  @ApiProperty({
    description: 'Sample variables for preview',
    example: {
      user: { name: 'John Doe' },
      session: { id: 'session_123' },
      conversation: { messageCount: 5 }
    }
  })
  @IsObject()
  @IsNotEmpty()
  sampleVariables: Record<string, any>;
}

export class CreateTemplateDto {
  @ApiProperty({
    description: 'Template name',
    example: 'Customer Support Template'
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Template string',
    example: 'Hello {{user.name}}, how can I help you today? Your account status is {{user.status}}.'
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(5000)
  template: string;

  @ApiPropertyOptional({
    description: 'System prompt for the template',
    example: 'You are a helpful customer support agent.'
  })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  systemPrompt?: string;

  @ApiPropertyOptional({
    description: 'Template variables configuration',
    example: [
      { name: 'user.name', type: 'string', required: true },
      { name: 'user.status', type: 'string', required: false, defaultValue: 'active' }
    ]
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => TemplateVariableDto)
  variables?: TemplateVariableDto[];

  @ApiPropertyOptional({
    description: 'Template metadata',
    example: { category: 'support', version: '1.0' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class TemplateVariableDto {
  @ApiProperty({
    description: 'Variable name',
    example: 'user.name'
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Variable type',
    example: 'string',
    enum: ['string', 'number', 'boolean', 'object', 'array']
  })
  @IsString()
  @IsNotEmpty()
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';

  @ApiProperty({
    description: 'Whether the variable is required',
    example: true
  })
  @IsOptional()
  required?: boolean;

  @ApiPropertyOptional({
    description: 'Default value for the variable',
    example: 'Guest'
  })
  @IsOptional()
  defaultValue?: any;

  @ApiPropertyOptional({
    description: 'Variable description',
    example: 'The user\'s display name'
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  description?: string;
}
