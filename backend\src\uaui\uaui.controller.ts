import { 
  Controller, 
  Post, 
  Get, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  Logger,
  HttpException,
  HttpStatus,
  Sse,
  MessageEvent
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger';
import { Observable, map } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';

// Guards
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RbacGuard } from '../auth/guards/rbac.guard';
import { TenantGuard } from '../auth/guards/tenant.guard';

// Decorators
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { CurrentTenant } from '../auth/decorators/current-tenant.decorator';

// DTOs
import { ProcessRequestDto, ProcessStreamingRequestDto } from './dto/uaui-request.dto';
import { UauiResponseDto } from './dto/uaui-response.dto';

// Services
import { UauiCoreService } from './services/uaui-core.service';
import { ContextManagerService } from './services/context-manager.service';
import { PromptTemplateService } from './services/prompt-template.service';
import { ConversationHistoryService } from './services/conversation-history.service';

// Interfaces
import { UauiRequest, UauiResponse } from './interfaces/uaui.interface';

@ApiTags('UAUI Engine')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RbacGuard, TenantGuard)
@Controller('uaui')
export class UauiController {
  private readonly logger = new Logger(UauiController.name);

  constructor(
    private readonly uauiCoreService: UauiCoreService,
    private readonly contextManagerService: ContextManagerService,
    private readonly promptTemplateService: PromptTemplateService,
    private readonly conversationHistoryService: ConversationHistoryService,
  ) {}

  @Post('process')
  @Roles('user', 'admin')
  @ApiOperation({ summary: 'Process a UAUI request' })
  @ApiResponse({ status: 200, description: 'Request processed successfully', type: UauiResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  @ApiResponse({ status: 500, description: 'Processing error' })
  async processRequest(
    @Body() processRequestDto: ProcessRequestDto,
    @CurrentUser() user: any,
    @CurrentTenant() tenant: any,
  ): Promise<UauiResponseDto> {
    try {
      const request: UauiRequest = {
        sessionId: processRequestDto.sessionId,
        userId: user.id,
        tenantId: tenant.id,
        message: processRequestDto.message,
        agentId: processRequestDto.agentId,
        context: processRequestDto.context,
        metadata: processRequestDto.metadata,
        requestId: uuidv4(),
        timestamp: new Date(),
      };

      this.logger.debug(`Processing UAUI request for user ${user.id}, session ${request.sessionId}`);

      const response = await this.uauiCoreService.processRequest(request);

      return {
        requestId: response.requestId,
        sessionId: response.sessionId,
        response: response.response,
        streaming: response.streaming,
        context: response.context,
        metadata: response.metadata,
        timestamp: response.timestamp,
        processingTime: response.processingTime,
        tokenUsage: response.tokenUsage,
      };

    } catch (error) {
      this.logger.error('Error processing UAUI request:', error);
      throw new HttpException(
        {
          message: 'Failed to process request',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('process/stream')
  @Roles('user', 'admin')
  @ApiOperation({ summary: 'Process a streaming UAUI request' })
  @ApiResponse({ status: 200, description: 'Streaming response initiated' })
  @Sse('stream')
  async processStreamingRequest(
    @Body() processRequestDto: ProcessStreamingRequestDto,
    @CurrentUser() user: any,
    @CurrentTenant() tenant: any,
  ): Promise<Observable<MessageEvent>> {
    try {
      const request: UauiRequest = {
        sessionId: processRequestDto.sessionId,
        userId: user.id,
        tenantId: tenant.id,
        message: processRequestDto.message,
        agentId: processRequestDto.agentId,
        context: processRequestDto.context,
        metadata: processRequestDto.metadata,
        requestId: uuidv4(),
        timestamp: new Date(),
      };

      this.logger.debug(`Processing streaming UAUI request for user ${user.id}, session ${request.sessionId}`);

      const streamingResponse = await this.uauiCoreService.processStreamingRequest(request);

      // Convert async iterable to Observable
      return new Observable<MessageEvent>(observer => {
        (async () => {
          try {
            for await (const chunk of streamingResponse.chunks) {
              observer.next({
                data: JSON.stringify({
                  type: 'chunk',
                  requestId: streamingResponse.requestId,
                  sessionId: streamingResponse.sessionId,
                  content: chunk,
                  timestamp: new Date(),
                }),
              });
            }

            // Send completion event
            observer.next({
              data: JSON.stringify({
                type: 'complete',
                requestId: streamingResponse.requestId,
                sessionId: streamingResponse.sessionId,
                timestamp: new Date(),
              }),
            });

            observer.complete();
          } catch (error) {
            observer.error(error);
          }
        })();
      });

    } catch (error) {
      this.logger.error('Error processing streaming UAUI request:', error);
      throw new HttpException(
        {
          message: 'Failed to process streaming request',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('context/:sessionId')
  @Roles('user', 'admin')
  @ApiOperation({ summary: 'Get session context' })
  @ApiParam({ name: 'sessionId', description: 'Session ID' })
  @ApiResponse({ status: 200, description: 'Session context retrieved' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  async getSessionContext(
    @Param('sessionId') sessionId: string,
    @CurrentUser() user: any,
  ) {
    try {
      const context = await this.contextManagerService.getSessionContext(sessionId);
      
      if (!context) {
        throw new HttpException('Session not found', HttpStatus.NOT_FOUND);
      }

      // Verify user has access to this session
      if (context.userId !== user.id && user.role !== 'admin') {
        throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
      }

      return {
        sessionId: context.sessionId,
        variables: context.variables,
        memory: context.memory,
        lastActivity: context.lastActivity,
        metadata: context.metadata,
        conversationHistory: context.conversationHistory,
      };

    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`Error getting session context for ${sessionId}:`, error);
      throw new HttpException(
        'Failed to retrieve session context',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('history/:sessionId')
  @Roles('user', 'admin')
  @ApiOperation({ summary: 'Get conversation history' })
  @ApiParam({ name: 'sessionId', description: 'Session ID' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of messages to retrieve' })
  @ApiResponse({ status: 200, description: 'Conversation history retrieved' })
  async getConversationHistory(
    @Param('sessionId') sessionId: string,
    @Query('limit') limit: string = '50',
    @CurrentUser() user: any,
  ) {
    try {
      // Verify user has access to this session
      const context = await this.contextManagerService.getSessionContext(sessionId);
      if (!context) {
        throw new HttpException('Session not found', HttpStatus.NOT_FOUND);
      }

      if (context.userId !== user.id && user.role !== 'admin') {
        throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
      }

      const messages = await this.conversationHistoryService.getRecentContext(
        sessionId,
        parseInt(limit, 10)
      );

      return {
        sessionId,
        messages,
        totalMessages: messages.length,
      };

    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`Error getting conversation history for ${sessionId}:`, error);
      throw new HttpException(
        'Failed to retrieve conversation history',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('history/:sessionId/search')
  @Roles('user', 'admin')
  @ApiOperation({ summary: 'Search conversation history' })
  @ApiParam({ name: 'sessionId', description: 'Session ID' })
  @ApiQuery({ name: 'query', description: 'Search query' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results to return' })
  @ApiResponse({ status: 200, description: 'Search results retrieved' })
  async searchConversationHistory(
    @Param('sessionId') sessionId: string,
    @Query('query') query: string,
    @Query('limit') limit: string = '10',
    @CurrentUser() user: any,
  ) {
    try {
      // Verify user has access to this session
      const context = await this.contextManagerService.getSessionContext(sessionId);
      if (!context) {
        throw new HttpException('Session not found', HttpStatus.NOT_FOUND);
      }

      if (context.userId !== user.id && user.role !== 'admin') {
        throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
      }

      const results = await this.conversationHistoryService.searchHistory(
        sessionId,
        query,
        parseInt(limit, 10)
      );

      return {
        sessionId,
        query,
        results,
        totalResults: results.length,
      };

    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`Error searching conversation history for ${sessionId}:`, error);
      throw new HttpException(
        'Failed to search conversation history',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('history/:sessionId/stats')
  @Roles('user', 'admin')
  @ApiOperation({ summary: 'Get conversation statistics' })
  @ApiParam({ name: 'sessionId', description: 'Session ID' })
  @ApiResponse({ status: 200, description: 'Conversation statistics retrieved' })
  async getConversationStatistics(
    @Param('sessionId') sessionId: string,
    @CurrentUser() user: any,
  ) {
    try {
      // Verify user has access to this session
      const context = await this.contextManagerService.getSessionContext(sessionId);
      if (!context) {
        throw new HttpException('Session not found', HttpStatus.NOT_FOUND);
      }

      if (context.userId !== user.id && user.role !== 'admin') {
        throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
      }

      const stats = await this.conversationHistoryService.getStatistics(sessionId);

      return {
        sessionId,
        statistics: stats,
      };

    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`Error getting conversation statistics for ${sessionId}:`, error);
      throw new HttpException(
        'Failed to retrieve conversation statistics',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('templates/variables')
  @Roles('user', 'admin')
  @ApiOperation({ summary: 'Get available template variables' })
  @ApiResponse({ status: 200, description: 'Available template variables retrieved' })
  getAvailableTemplateVariables() {
    try {
      const variables = this.promptTemplateService.getAvailableVariables();
      
      return {
        variables,
        totalCount: variables.length,
      };

    } catch (error) {
      this.logger.error('Error getting available template variables:', error);
      throw new HttpException(
        'Failed to retrieve template variables',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
